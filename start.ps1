# Claude Code Router PowerShell 启动脚本
param(
    [string]$Action = "menu"
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 设置工作目录
Set-Location $PSScriptRoot

function Show-Banner {
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                    Claude Code Router                        ║" -ForegroundColor Cyan
    Write-Host "║                      PowerShell 启动脚本                      ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

function Show-Menu {
    Write-Host "请选择操作：" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "[1] 启动服务" -ForegroundColor Green
    Write-Host "[2] 停止服务" -ForegroundColor Red
    Write-Host "[3] 重启服务" -ForegroundColor Blue
    Write-Host "[4] 查看状态" -ForegroundColor Cyan
    Write-Host "[5] 测试连接" -ForegroundColor Magenta
    Write-Host "[6] 查看日志" -ForegroundColor Gray
    Write-Host "[7] 快速测试" -ForegroundColor DarkYellow
    Write-Host "[8] 退出" -ForegroundColor White
    Write-Host ""
}

function Start-Service {
    Write-Host "🚀 正在启动 Claude Code Router..." -ForegroundColor Green
    try {
        $result = node dist/cli.js start
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 服务启动成功！" -ForegroundColor Green
        } else {
            Write-Host "❌ 服务启动失败！" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 启动过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Stop-Service {
    Write-Host "🛑 正在停止 Claude Code Router..." -ForegroundColor Red
    try {
        $result = node dist/cli.js stop
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 服务停止成功！" -ForegroundColor Green
        } else {
            Write-Host "❌ 服务停止失败！" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 停止过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Restart-Service {
    Write-Host "🔄 正在重启 Claude Code Router..." -ForegroundColor Blue
    try {
        $result = node dist/cli.js restart
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 服务重启成功！" -ForegroundColor Green
        } else {
            Write-Host "❌ 服务重启失败！" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 重启过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Show-Status {
    Write-Host "📊 查看服务状态..." -ForegroundColor Cyan
    try {
        node dist/cli.js status
    } catch {
        Write-Host "❌ 获取状态失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Test-Connection {
    Write-Host "🔍 测试API连接..." -ForegroundColor Magenta
    if (Test-Path "test_api.js") {
        try {
            node test_api.js
        } catch {
            Write-Host "❌ 测试失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 测试文件不存在，请先创建 test_api.js" -ForegroundColor Red
    }
}

function Show-Logs {
    Write-Host "📄 查看日志文件..." -ForegroundColor Gray
    $logFile = "$env:USERPROFILE\.claude-code-router\claude-code-router.log"
    if (Test-Path $logFile) {
        Write-Host "日志文件位置: $logFile" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "最近10行日志:" -ForegroundColor Yellow
        Get-Content $logFile -Tail 10
    } else {
        Write-Host "❌ 日志文件不存在" -ForegroundColor Red
    }
}

function Quick-Test {
    Write-Host "⚡ 快速测试..." -ForegroundColor DarkYellow
    try {
        Write-Host "正在测试 Claude Code 命令..." -ForegroundColor Yellow
        $result = node dist/cli.js code "请简单介绍一下你自己"
        Write-Host "✅ 快速测试完成！" -ForegroundColor Green
    } catch {
        Write-Host "❌ 快速测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 主程序
if ($Action -eq "start") {
    Show-Banner
    Start-Service
    return
} elseif ($Action -eq "stop") {
    Show-Banner
    Stop-Service
    return
} elseif ($Action -eq "restart") {
    Show-Banner
    Restart-Service
    return
} elseif ($Action -eq "status") {
    Show-Banner
    Show-Status
    return
}

# 交互式菜单
Show-Banner

while ($true) {
    Show-Menu
    $choice = Read-Host "请输入选项 (1-8)"
    
    switch ($choice) {
        "1" { 
            Start-Service
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "2" { 
            Stop-Service
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "3" { 
            Restart-Service
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "4" { 
            Show-Status
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "5" { 
            Test-Connection
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "6" { 
            Show-Logs
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "7" { 
            Quick-Test
            Write-Host ""
            Read-Host "按回车键继续..."
        }
        "8" { 
            Write-Host ""
            Write-Host "👋 再见！" -ForegroundColor Green
            break
        }
        default { 
            Write-Host "无效选项，请重新选择" -ForegroundColor Red
        }
    }
    
    Clear-Host
    Show-Banner
}
