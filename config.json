{"LOG": true, "API_TIMEOUT_MS": 600000, "Providers": [{"name": "modelscope", "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions", "api_key": "ms-1740787b-cc2b-4f42-b1f9-81dcb527232b", "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct", "moonshotai/Kimi-K2-Instruct"], "transformer": {"use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"]}}], "Router": {"default": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct", "think": "modelscope,moonshotai/Kimi-K2-Instruct", "longContext": "modelscope,moonshotai/Kimi-K2-Instruct", "longContextThreshold": 60000}}