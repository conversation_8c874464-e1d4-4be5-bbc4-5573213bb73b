const http = require('http');

// 测试通过路由器访问Kimi模型
function testKimiThroughRouter() {
  return new Promise((resolve, reject) => {
    const testData = JSON.stringify({
      model: "modelscope,moonshotai/Kimi-K2-Instruct", // 直接指定模型
      max_tokens: 150,
      messages: [{
        role: "user",
        content: "你好，请介绍一下你自己，我想确认你是Kimi还是Qwen"
      }]
    });

    const options = {
      hostname: '127.0.0.1',
      port: 3456,
      path: '/v1/messages',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(testData)
      }
    };

    console.log('🔍 通过路由器测试 Kimi-K2-Instruct 模型...');

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.content && response.content[0]) {
            console.log('✅ Kimi模型通过路由器测试成功!');
            console.log('完整响应:', response.content[0].text);
            resolve(true);
          } else {
            console.log('❌ 响应格式异常:', data);
            resolve(false);
          }
        } catch (error) {
          console.log('❌ 解析失败:', error.message);
          console.log('原始响应:', data);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ 请求失败:', error.message);
      resolve(false);
    });

    req.write(testData);
    req.end();
  });
}

// 运行测试
testKimiThroughRouter().then(success => {
  if (success) {
    console.log('\n🎉 Kimi模型已成功添加到Claude Code Router！');
    console.log('现在你可以在配置中使用智能路由来自动选择最适合的模型。');
  } else {
    console.log('\n⚠️  Kimi模型测试失败，请检查配置');
  }
}).catch(console.error);
