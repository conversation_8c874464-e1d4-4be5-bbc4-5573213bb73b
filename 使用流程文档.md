# Claude Code Router 使用流程文档

## 📋 目录
- [项目简介](#项目简介)
- [环境要求](#环境要求)
- [安装配置](#安装配置)
- [基本使用](#基本使用)
- [高级配置](#高级配置)
- [常见问题](#常见问题)
- [最佳实践](#最佳实践)

## 🎯 项目简介

Claude Code Router 是一个强大的代理工具，允许你将 Claude Code 的请求路由到不同的 LLM 提供商（如魔搭、DeepSeek、Ollama等），实现：

- **成本优化** - 使用更便宜的API替代Anthropic官方API
- **模型选择** - 根据任务类型自动选择最适合的模型
- **无缝集成** - Claude Code无需任何修改即可使用
- **多提供商支持** - 支持国内外多种AI服务提供商

## 💻 环境要求

### 必需软件
- **Node.js** >= 16.0.0
- **npm** 或 **yarn** 或 **pnpm**
- **Claude Code** (VS Code扩展)

### 系统支持
- Windows 10/11
- macOS 10.15+
- Linux (Ubuntu 18.04+)

## 🚀 安装配置

### 第一步：安装 Claude Code
```bash
# 在VS Code中安装Claude Code扩展
# 或通过命令行安装
npm install -g @anthropic-ai/claude-code
```

### 第二步：获取项目代码
```bash
# 克隆项目
git clone https://github.com/musistudio/claude-code-router.git
cd claude-code-router

# 安装依赖
npm install

# 构建项目
npm run build
```

### 第三步：配置API密钥

#### 3.1 获取API密钥
根据你选择的提供商获取API密钥：

**魔搭 (ModelScope)**
- 访问：https://modelscope.cn/
- 注册账号并获取API密钥
- 格式：`ms-xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`

**DeepSeek**
- 访问：https://platform.deepseek.com/
- 注册账号并获取API密钥
- 格式：`sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

**其他提供商**
- OpenRouter: https://openrouter.ai/
- Ollama: 本地部署，无需密钥

#### 3.2 创建配置文件
```bash
# 创建配置目录
mkdir -p ~/.claude-code-router

# 创建配置文件
touch ~/.claude-code-router/config.json
```

#### 3.3 配置示例

**简单配置（仅魔搭）**
```json
{
  "LOG": true,
  "API_TIMEOUT_MS": 600000,
  "Providers": [
    {
      "name": "modelscope",
      "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions",
      "api_key": "你的魔搭API密钥",
      "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct"],
      "transformer": {
        "use": [
          ["maxtoken", {"max_tokens": 65536}],
          "enhancetool"
        ]
      }
    }
  ],
  "Router": {
    "default": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct"
  }
}
```

## 🎮 基本使用

### 启动服务

#### 方法一：直接启动
```bash
# 在项目目录下
node dist/cli.js start
```

#### 方法二：后台启动
```bash
# 启动服务
node dist/cli.js start &

# 查看状态
node dist/cli.js status

# 停止服务
node dist/cli.js stop

# 重启服务
node dist/cli.js restart
```

### 使用 Claude Code

#### 在 VS Code 中使用
1. 确保 Claude Code Router 服务已启动
2. 打开 VS Code
3. 按 `Cmd+Shift+P` (Mac) 或 `Ctrl+Shift+P` (Windows/Linux)
4. 输入 "Claude Code" 并选择相关命令
5. 开始与 Claude 对话

#### 命令行使用
```bash
# 直接执行代码任务
node dist/cli.js code "写一个Python函数来计算斐波那契数列"

# 如果服务未启动，会自动启动服务
node dist/cli.js code "帮我分析这个错误日志"
```

### 验证配置

#### 测试API连接
```bash
# 创建测试脚本
cat > test_api.js << 'EOF'
const http = require('http');

const testData = JSON.stringify({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 100,
  messages: [{
    role: "user",
    content: "Hello, please respond in Chinese."
  }]
});

const options = {
  hostname: '127.0.0.1',
  port: 3456,
  path: '/v1/messages',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(testData)
  }
};

const req = http.request(options, (res) => {
  let data = '';
  res.on('data', (chunk) => data += chunk);
  res.on('end', () => {
    const response = JSON.parse(data);
    console.log('✅ API测试成功!');
    console.log('响应:', response.content[0].text);
  });
});

req.on('error', (error) => {
  console.error('❌ 测试失败:', error.message);
});

req.write(testData);
req.end();
EOF

# 运行测试
node test_api.js
```

## ⚙️ 高级配置

### 智能路由配置

Claude Code Router 支持根据不同场景自动选择模型：

```json
{
  "Router": {
    "default": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "background": "ollama,qwen2.5-coder:latest",
    "think": "deepseek,deepseek-reasoner",
    "longContext": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "longContextThreshold": 60000,
    "webSearch": "deepseek,deepseek-chat"
  }
}
```

**路由规则说明：**
- `default`: 默认模型，用于一般任务
- `background`: 后台任务模型（通常选择本地或便宜的模型）
- `think`: 推理密集型任务模型
- `longContext`: 长上下文模型（当token数超过阈值时使用）
- `longContextThreshold`: 长上下文阈值（默认60000）
- `webSearch`: 网络搜索任务模型

### 完整配置示例（多提供商）

```json
{
  "LOG": true,
  "API_TIMEOUT_MS": 600000,
  "APIKEY": "your-secret-key",
  "HOST": "127.0.0.1",
  "Providers": [
    {
      "name": "modelscope",
      "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions",
      "api_key": "你的魔搭API密钥",
      "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct"],
      "transformer": {
        "use": [["maxtoken", {"max_tokens": 65536}], "enhancetool"]
      }
    },
    {
      "name": "deepseek",
      "api_base_url": "https://api.deepseek.com/chat/completions",
      "api_key": "你的DeepSeek API密钥",
      "models": ["deepseek-chat", "deepseek-reasoner"],
      "transformer": {
        "use": ["deepseek"],
        "deepseek-chat": {"use": ["tooluse"]}
      }
    },
    {
      "name": "ollama",
      "api_base_url": "http://localhost:11434/v1/chat/completions",
      "api_key": "ollama",
      "models": ["qwen2.5-coder:latest"]
    }
  ],
  "Router": {
    "default": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "background": "ollama,qwen2.5-coder:latest",
    "think": "deepseek,deepseek-reasoner",
    "longContext": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct",
    "longContextThreshold": 60000
  }
}
```

### 自定义路由脚本

创建自定义路由逻辑：

```javascript
// ~/.claude-code-router/custom-router.js
module.exports = async function router(req, config) {
  const userMessage = req.body.messages.find(m => m.role === "user")?.content;

  // 代码相关任务使用编程专用模型
  if (userMessage && (userMessage.includes("代码") || userMessage.includes("编程"))) {
    return "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct";
  }

  // 推理任务使用推理模型
  if (userMessage && (userMessage.includes("分析") || userMessage.includes("思考"))) {
    return "deepseek,deepseek-reasoner";
  }

  // 其他情况使用默认路由
  return null;
};
```

在配置文件中启用：
```json
{
  "CUSTOM_ROUTER_PATH": "~/.claude-code-router/custom-router.js"
}
```

### 转换器配置

转换器用于适配不同提供商的API格式：

```json
{
  "transformer": {
    "use": [
      "deepseek",           // DeepSeek API适配
      "tooluse",           // 工具使用优化
      ["maxtoken", {"max_tokens": 32768}], // 设置最大token数
      "enhancetool"        // 增强工具支持
    ],
    "特定模型名": {
      "use": ["reasoning"]  // 模型特定转换器
    }
  }
}
```

**内置转换器：**
- `deepseek`: DeepSeek API适配
- `gemini`: Google Gemini API适配
- `openrouter`: OpenRouter API适配
- `tooluse`: 工具使用优化
- `maxtoken`: 设置最大token数
- `enhancetool`: 增强工具支持
- `reasoning`: 推理模式支持

### 安全配置

```json
{
  "APIKEY": "your-secret-key",    // API访问密钥
  "HOST": "127.0.0.1",           // 绑定地址（生产环境建议127.0.0.1）
  "PROXY_URL": "http://127.0.0.1:7890"  // 代理设置
}
```

## 🔧 常见问题

### Q1: 服务启动失败
**症状**: 运行 `node dist/cli.js start` 后没有响应或报错

**解决方案**:
```bash
# 检查端口占用
netstat -an | grep 3456

# 强制停止服务
node dist/cli.js stop

# 清理PID文件
rm ~/.claude-code-router/.claude-code-router.pid

# 重新启动
node dist/cli.js start
```

### Q2: API密钥无效
**症状**: 请求返回401或403错误

**解决方案**:
1. 检查API密钥格式是否正确
2. 确认API密钥是否有效且未过期
3. 检查API配额是否用完
4. 验证API端点URL是否正确

```bash
# 测试API密钥
curl -X POST "https://api-inference.modelscope.cn/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 你的API密钥" \
  -d '{"model":"Qwen/Qwen3-Coder-480B-A35B-Instruct","messages":[{"role":"user","content":"test"}],"max_tokens":10}'
```

### Q3: Claude Code 连接失败
**症状**: Claude Code 无法连接到路由器

**解决方案**:
1. 确认服务正在运行：`node dist/cli.js status`
2. 检查端口是否正确（默认3456）
3. 确认防火墙设置
4. 重启Claude Code扩展

### Q4: 响应速度慢
**症状**: 请求响应时间过长

**解决方案**:
1. 调整超时设置：
```json
{
  "API_TIMEOUT_MS": 300000  // 5分钟
}
```

2. 使用更快的模型：
```json
{
  "Router": {
    "default": "deepseek,deepseek-chat"  // 通常比大模型更快
  }
}
```

3. 启用本地模型：
```json
{
  "Router": {
    "background": "ollama,qwen2.5-coder:latest"
  }
}
```

### Q5: 中文乱码问题
**症状**: 返回的中文内容显示乱码

**解决方案**:
1. 确保配置文件使用UTF-8编码
2. 检查终端编码设置
3. 在请求中明确指定语言：
```json
{
  "messages": [
    {
      "role": "user",
      "content": "请用中文回答：你的问题"
    }
  ]
}
```

## 💡 最佳实践

### 1. 成本优化策略

**分层使用模型**:
```json
{
  "Router": {
    "default": "deepseek,deepseek-chat",           // 便宜的日常模型
    "think": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct", // 复杂任务用大模型
    "background": "ollama,qwen2.5-coder:latest"    // 本地模型处理简单任务
  }
}
```

**设置合理的token限制**:
```json
{
  "transformer": {
    "use": [
      ["maxtoken", {"max_tokens": 4096}]  // 根据需要调整
    ]
  }
}
```

### 2. 性能优化

**启用日志监控**:
```json
{
  "LOG": true
}
```

**使用连接池**:
```json
{
  "API_TIMEOUT_MS": 60000,  // 适中的超时时间
  "KEEP_ALIVE": true        // 保持连接
}
```

### 3. 安全建议

**生产环境配置**:
```json
{
  "APIKEY": "strong-random-key",  // 设置强密钥
  "HOST": "127.0.0.1",           // 仅本地访问
  "LOG": false                   // 生产环境关闭详细日志
}
```

**API密钥管理**:

- 定期轮换API密钥
- 使用环境变量存储敏感信息
- 监控API使用量和异常访问

### 4. 开发工作流

**项目初始化**:
```bash
# 1. 启动服务
node dist/cli.js start

# 2. 验证配置
node dist/cli.js status

# 3. 测试连接
node dist/cli.js code "Hello, test connection"
```

**日常使用**:
1. 在VS Code中直接使用Claude Code
2. 利用智能路由自动选择合适模型
3. 定期检查日志和使用统计

**调试技巧**:
```bash
# 查看详细日志
tail -f ~/.claude-code-router/claude-code-router.log

# 测试特定模型
curl -X POST "http://127.0.0.1:3456/v1/messages" \
  -H "Content-Type: application/json" \
  -d '{"model":"provider,model-name","messages":[...]}'
```

### 5. 扩展建议

**添加新提供商**:
1. 在配置文件中添加新的Provider
2. 配置相应的transformer
3. 测试API连接
4. 更新路由规则

**自定义功能**:

- 编写自定义转换器
- 实现自定义路由逻辑
- 集成监控和告警

---

## 📞 技术支持

如果遇到问题，可以：

1. **查看日志**: `~/.claude-code-router/claude-code-router.log`
2. **检查状态**: `node dist/cli.js status`
3. **重启服务**: `node dist/cli.js restart`
4. **提交Issue**: 在GitHub项目页面提交问题

---

**祝你使用愉快！🎉**
```
