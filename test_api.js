const https = require('https');
const http = require('http');

const testData = JSON.stringify({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 100,
  messages: [
    {
      role: "user",
      content: "请用中文回答：你好，请介绍一下你自己。"
    }
  ]
});

const options = {
  hostname: '127.0.0.1',
  port: 3456,
  path: '/v1/messages',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(testData)
  }
};

console.log('正在测试 Claude Code Router API...');

const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers)}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('\n✅ API 测试成功!');
      console.log('响应内容:');
      console.log(response.content[0].text);
    } catch (error) {
      console.log('\n❌ 解析响应失败:');
      console.log(data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ 请求失败:', error.message);
});

req.write(testData);
req.end();
