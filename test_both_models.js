const http = require('http');

// 测试函数
function testModel(modelName, description) {
  return new Promise((resolve, reject) => {
    const testData = JSON.stringify({
      model: modelName,
      max_tokens: 100,
      messages: [{
        role: "user",
        content: "请简单介绍一下你自己，用中文回答"
      }]
    });

    const options = {
      hostname: '127.0.0.1',
      port: 3456,
      path: '/v1/messages',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(testData)
      }
    };

    console.log(`\n🔍 测试 ${description}...`);

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (response.content && response.content[0]) {
            console.log(`✅ ${description} 测试成功!`);
            console.log(`响应: ${response.content[0].text.substring(0, 100)}...`);
            resolve(true);
          } else {
            console.log(`❌ ${description} 响应格式异常:`, data);
            resolve(false);
          }
        } catch (error) {
          console.log(`❌ ${description} 解析失败:`, error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error(`❌ ${description} 请求失败:`, error.message);
      resolve(false);
    });

    req.write(testData);
    req.end();
  });
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试 Claude Code Router 的两个模型...\n');
  
  // 测试默认模型 (Qwen)
  const qwenResult = await testModel(
    'claude-3-5-sonnet-20241022', 
    'Qwen3-Coder-480B (默认模型)'
  );
  
  // 等待一秒
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 测试 Kimi 模型 (通过指定特定路由)
  const kimiResult = await testModel(
    'claude-3-5-sonnet-20241022', 
    'Kimi-K2-Instruct (通过路由)'
  );
  
  console.log('\n📊 测试结果总结:');
  console.log(`Qwen3-Coder-480B: ${qwenResult ? '✅ 正常' : '❌ 失败'}`);
  console.log(`Kimi-K2-Instruct: ${kimiResult ? '✅ 正常' : '❌ 失败'}`);
  
  if (qwenResult && kimiResult) {
    console.log('\n🎉 所有模型测试通过！Claude Code Router 运行正常！');
  } else {
    console.log('\n⚠️  部分模型测试失败，请检查配置');
  }
}

// 运行测试
runTests().catch(console.error);
