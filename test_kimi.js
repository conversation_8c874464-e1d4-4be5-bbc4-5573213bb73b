const https = require('https');

const data = JSON.stringify({
  model: "moonshotai/Kimi-K2-Instruct",
  messages: [
    {
      role: "user",
      content: "你好，请简单介绍一下你自己"
    }
  ],
  max_tokens: 100
});

const options = {
  hostname: 'api-inference.modelscope.cn',
  port: 443,
  path: '/v1/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ms-1740787b-cc2b-4f42-b1f9-81dcb527232b',
    'Content-Length': Buffer.byteLength(data)
  }
};

console.log('🔍 正在测试 Kimi-K2-Instruct 模型...');

const req = https.request(options, (res) => {
  let responseData = '';
  
  res.on('data', (chunk) => {
    responseData += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(responseData);
      if (response.choices && response.choices[0]) {
        console.log('✅ Kimi模型测试成功!');
        console.log('模型响应:', response.choices[0].message.content);
        console.log('模型信息:', response.model);
      } else {
        console.log('❌ 响应格式异常:', responseData);
      }
    } catch (error) {
      console.log('❌ 解析响应失败:', error.message);
      console.log('原始响应:', responseData);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ 请求失败:', error.message);
});

req.write(data);
req.end();
