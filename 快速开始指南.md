# Claude Code Router 快速开始指南

## 🚀 5分钟快速上手

### 前提条件
- ✅ 已安装 Node.js (>= 16.0.0)
- ✅ 已安装 Claude Code VS Code 扩展
- ✅ 已获取魔搭API密钥

### 第一步：项目准备
```bash
# 1. 进入项目目录
cd c:\Users\<USER>\Desktop\claude-code-router

# 2. 安装依赖（如果还没安装）
npm install

# 3. 构建项目（如果还没构建）
npm run build
```

### 第二步：配置文件
你的配置文件已经创建在：`C:\Users\<USER>\.claude-code-router\config.json`

当前配置内容：
```json
{
  "LOG": true,
  "API_TIMEOUT_MS": 600000,
  "Providers": [
    {
      "name": "modelscope",
      "api_base_url": "https://api-inference.modelscope.cn/v1/chat/completions",
      "api_key": "ms-1740787b-cc2b-4f42-b1f9-81dcb527232b",
      "models": ["Qwen/Qwen3-Coder-480B-A35B-Instruct"],
      "transformer": {
        "use": [
          ["maxtoken", {"max_tokens": 65536}],
          "enhancetool"
        ]
      }
    }
  ],
  "Router": {
    "default": "modelscope,Qwen/Qwen3-Coder-480B-A35B-Instruct"
  }
}
```

### 第三步：启动服务
```bash
# 启动 Claude Code Router 服务
node dist/cli.js start
```

成功启动后会看到：
```
🚀 LLMs API server listening on http://127.0.0.1:3456
```

### 第四步：验证服务
```bash
# 检查服务状态
node dist/cli.js status
```

应该显示：
```
✅ Status: Running
🆔 Process ID: [进程ID]
🌐 Port: 3456
📡 API Endpoint: http://127.0.0.1:3456
```

### 第五步：测试连接
```bash
# 测试 Claude Code 命令
node dist/cli.js code "写一个Hello World程序"
```

## 🎯 在 VS Code 中使用

### 方法一：命令面板
1. 打开 VS Code
2. 按 `Ctrl+Shift+P` (Windows) 或 `Cmd+Shift+P` (Mac)
3. 输入 "Claude Code"
4. 选择 "Claude Code: Start Chat"

### 方法二：快捷键
- 按 `Cmd+Esc` (Mac) 或 `Ctrl+Esc` (Windows) 快速启动

### 方法三：右键菜单
1. 在代码文件中右键
2. 选择 "Ask Claude Code"

## 📝 常用命令

### 服务管理
```bash
# 启动服务
node dist/cli.js start

# 停止服务
node dist/cli.js stop

# 重启服务
node dist/cli.js restart

# 查看状态
node dist/cli.js status
```

### 直接使用
```bash
# 代码生成
node dist/cli.js code "写一个Python排序算法"

# 代码解释
node dist/cli.js code "解释这段代码的作用"

# 错误调试
node dist/cli.js code "帮我修复这个错误"
```

## 🔍 验证配置

### 测试API连接
创建测试文件 `test.js`：
```javascript
const http = require('http');

const data = JSON.stringify({
  model: "claude-3-5-sonnet-20241022",
  max_tokens: 100,
  messages: [{ role: "user", content: "你好，请用中文回答" }]
});

const options = {
  hostname: '127.0.0.1',
  port: 3456,
  path: '/v1/messages',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(data)
  }
};

const req = http.request(options, (res) => {
  let responseData = '';
  res.on('data', (chunk) => responseData += chunk);
  res.on('end', () => {
    const response = JSON.parse(responseData);
    console.log('✅ 测试成功!');
    console.log('回复:', response.content[0].text);
  });
});

req.on('error', (error) => {
  console.error('❌ 测试失败:', error.message);
});

req.write(data);
req.end();
```

运行测试：
```bash
node test.js
```

## 🎨 使用示例

### 代码生成
在 Claude Code 中输入：
```
写一个Python函数来计算斐波那契数列，要求：
1. 支持递归和迭代两种方式
2. 包含完整的文档字符串
3. 添加输入验证
```

### 代码审查
```
请审查这段代码并提出改进建议：
[粘贴你的代码]
```

### 错误调试
```
我的代码出现了以下错误，请帮我分析和修复：
错误信息：[错误信息]
代码：[问题代码]
```

### 代码重构
```
请帮我重构这段代码，使其更加简洁和高效：
[粘贴代码]
```

## ⚡ 性能优化建议

### 1. 调整token限制
如果响应太长，可以在配置中调整：
```json
{
  "transformer": {
    "use": [
      ["maxtoken", {"max_tokens": 4096}]
    ]
  }
}
```

### 2. 启用日志
查看详细日志帮助调试：
```bash
# 查看日志文件
tail -f ~/.claude-code-router/claude-code-router.log
```

### 3. 网络优化
如果网络较慢，可以增加超时时间：
```json
{
  "API_TIMEOUT_MS": 900000
}
```

## 🔧 故障排除

### 问题1：服务启动失败
```bash
# 检查端口占用
netstat -an | findstr 3456

# 强制停止并重启
node dist/cli.js stop
node dist/cli.js start
```

### 问题2：Claude Code 连接失败
1. 确认服务正在运行：`node dist/cli.js status`
2. 检查VS Code中Claude Code扩展是否启用
3. 重启VS Code

### 问题3：API响应慢
1. 检查网络连接
2. 尝试使用其他模型
3. 调整超时设置

## 📚 下一步

现在你已经成功配置并运行了 Claude Code Router！

**推荐阅读：**
- [完整使用流程文档](使用流程文档.md) - 了解高级配置
- [项目README](README.md) - 了解更多功能特性

**进阶配置：**
- 添加更多AI提供商（DeepSeek、Ollama等）
- 配置智能路由规则
- 自定义转换器

**获取帮助：**
- 查看日志：`~/.claude-code-router/claude-code-router.log`
- GitHub Issues：提交问题和建议

---

🎉 **恭喜！你现在可以享受使用Claude Code的便利，同时节省API成本！**
