@echo off
chcp 65001 >nul
title Claude Code Router

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    Claude Code Router                        ║
echo ║                      启动脚本                                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

:menu
echo 请选择操作：
echo.
echo [1] 启动服务
echo [2] 停止服务  
echo [3] 重启服务
echo [4] 查看状态
echo [5] 测试连接
echo [6] 查看日志
echo [7] 退出
echo.
set /p choice=请输入选项 (1-7): 

if "%choice%"=="1" goto start
if "%choice%"=="2" goto stop
if "%choice%"=="3" goto restart
if "%choice%"=="4" goto status
if "%choice%"=="5" goto test
if "%choice%"=="6" goto logs
if "%choice%"=="7" goto exit
echo 无效选项，请重新选择
goto menu

:start
echo.
echo 🚀 正在启动 Claude Code Router...
node dist/cli.js start
if %errorlevel%==0 (
    echo ✅ 服务启动成功！
) else (
    echo ❌ 服务启动失败！
)
echo.
pause
goto menu

:stop
echo.
echo 🛑 正在停止 Claude Code Router...
node dist/cli.js stop
if %errorlevel%==0 (
    echo ✅ 服务停止成功！
) else (
    echo ❌ 服务停止失败！
)
echo.
pause
goto menu

:restart
echo.
echo 🔄 正在重启 Claude Code Router...
node dist/cli.js restart
if %errorlevel%==0 (
    echo ✅ 服务重启成功！
) else (
    echo ❌ 服务重启失败！
)
echo.
pause
goto menu

:status
echo.
echo 📊 查看服务状态...
node dist/cli.js status
echo.
pause
goto menu

:test
echo.
echo 🔍 测试API连接...
if exist test_api.js (
    node test_api.js
) else (
    echo ❌ 测试文件不存在，请先创建 test_api.js
)
echo.
pause
goto menu

:logs
echo.
echo 📄 查看日志文件...
set logfile=%USERPROFILE%\.claude-code-router\claude-code-router.log
if exist "%logfile%" (
    echo 日志文件位置: %logfile%
    echo.
    echo 最近10行日志:
    powershell -Command "Get-Content '%logfile%' -Tail 10"
) else (
    echo ❌ 日志文件不存在
)
echo.
pause
goto menu

:exit
echo.
echo 👋 再见！
exit /b 0
